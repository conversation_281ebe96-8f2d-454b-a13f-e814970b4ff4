<?php
// 提成管理页
session_start();
require_once '../includes/auth.php'; // 引入权限验证文件
require_once '../includes/config.php'; // 引入数据库连接
require_once '../includes/functions.php'; // 引入函数文件

// 确保管理员已登录
requireAdminLogin();

// 获取用户ID并从数据库中获取用户信息
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    $userQuery = "SELECT name, email, phone, avatar FROM x_users WHERE user_id = ?";
    $stmt = $pdo->prepare($userQuery);
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    // 检查用户是否存在
    if ($user) {
        // 如果avatar字段内容小于5个字符，使用默认值
        if (strlen($user['avatar']) < 5) {
            $user['avatar'] = 'images/avatars/gly_default_avatar.jpg';
        }

        // 赋值给对应的变量
        $user_name = htmlspecialchars($user['name']);
        $user_email = htmlspecialchars($user['email']);
        $user_phone = htmlspecialchars($user['phone']);
        $user_avatar = htmlspecialchars($user['avatar']);

        error_log("User data: " . print_r($user, true)); // 打印用户数据
    } else {
        // 用户不存在的处理
        $user_name = '访客'; // 或者其他默认值
        $user_email = '';
        $user_phone = '';
        $user_avatar = 'images/avatars/gly_default_avatar.jpg'; // 默认头像
    }
} else {
    // 如果用户未登录，初始化变量
    $user_id = null;
    $user_name = '访客'; // 默认值
    $user_email = '';
    $user_phone = '';
    $user_avatar = 'images/avatars/gly_default_avatar.jpg'; // 默认头像
}


// 处理所有 AJAX 请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    try {
        // 获取请求数据
        $postData = !empty($_POST) ? $_POST : json_decode(file_get_contents('php://input'), true);
        
        if (empty($postData['action'])) {
            throw new Exception('缺少必要的 action 参数');
        }

        switch ($postData['action']) {
            case 'batch_payment':
                // 批量设置已支付
                if (empty($postData['ids']) || empty($postData['payment_method'])) {
                    throw new Exception('缺少必要的参数');
                }

                $currentTime = date('Y-m-d H:i:s');
                $sql = "UPDATE x_rebates SET 
                        is_withdrawn = 1,
                        payment_method = ?,
                        payment_date = ?,
                        updated_at = ?,
                        last_updated_user_id = ?
                        WHERE rebate_id IN (" . implode(',', array_map('intval', $postData['ids'])) . ")";
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$postData['payment_method'], $currentTime, $currentTime, $_SESSION['user_id']]);
                break;

            case 'add_commission':
                // 添加提成记录
                if (empty($postData['salesman_id']) || empty($postData['account_id']) || 
                    empty($postData['amount']) || empty($postData['rebate_month'])) {
                    throw new Exception('所有字段都是必填项');
                }

                // 获取关联的冗余字段信息
                $stmt = $pdo->prepare("SELECT netbar_account, netbar_name FROM x_accounts WHERE account_id = ?");
                $stmt->execute([$postData['account_id']]);
                $accountInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $stmt = $pdo->prepare("SELECT name, phone FROM x_salesmen WHERE salesman_id = ?");
                $stmt->execute([$postData['salesman_id']]);
                $salesmanInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                // 处理日期和默认值
                $currentTime = date('Y-m-d H:i:s');
                $rebate_month = $postData['rebate_month'] . '-01';
                $withdrawal_allowed_date = $postData['withdrawal_allowed_date'] ?: date('Y-m-d H:i:s', strtotime('+30 days'));
                
                // 插入记录
                $sql = "INSERT INTO x_rebates (
                    user_id, salesman_id, salesman_name, salesman_phone, 
                    account_id, netbar_account, netbar_name, 
                    amount, rebate_month, created_at, 
                    withdrawal_allowed_date, is_withdrawn, 
                    withdrawal_requested_date, payment_method, 
                    payment_date, notes, updated_at, last_updated_user_id,
                    payment_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                $stmt = $pdo->prepare($sql);
                $stmt->execute([
                    $_SESSION['user_id'], 
                    $postData['salesman_id'],
                    $salesmanInfo['name'],
                    $salesmanInfo['phone'],
                    $postData['account_id'],
                    $accountInfo['netbar_account'],
                    $accountInfo['netbar_name'],
                    $postData['amount'], 
                    $rebate_month,
                    $currentTime,
                    $withdrawal_allowed_date,
                    $postData['is_withdrawn'] ?? 0,
                    $postData['withdrawal_requested_date'] ?: null,
                    $postData['payment_method'] ?: null,
                    $postData['payment_date'] ?: null,
                    $postData['notes'] ?: null,
                    $currentTime,
                    $_SESSION['user_id'],
                    $postData['payment_id']
                ]);
                break;

            default:
                throw new Exception('未知的操作类型');
        }

        echo json_encode(['success' => true]);
        
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ]);
    }
    exit;
}



// 分页逻辑
$perPageOptions = [30, 100, 500, 1000, 9000]; // 每页记录条数选项
$perPage = isset($_GET['per_page']) && in_array((int)$_GET['per_page'], $perPageOptions) ? (int)$_GET['per_page'] : 30; // 默认每页10条
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1; // 当前页码
$offset = ($page - 1) * $perPage; // 计算偏移量


// 处理搜索条件
$searchField = isset($_GET['search_field']) ? $_GET['search_field'] : null;
$searchValue = isset($_GET['search_value']) ? trim($_GET['search_value']) : null;
$whereClause = '';
$searchParams = [];

// 添加提现申请筛选条件
if ($searchField && $searchValue) {
    switch ($searchField) {
        case 'salesman_name':
            $whereClause = 'WHERE r.salesman_name LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
        case 'salesman_phone':
            $whereClause = 'WHERE r.salesman_phone LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
        case 'netbar_account':
            $whereClause = 'WHERE r.netbar_account LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
        case 'netbar_name':
            $whereClause = 'WHERE r.netbar_name LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
        case 'payment_id':
            $whereClause = 'WHERE r.payment_id = ?';
            $searchParams[] = $searchValue;
            break;
        case 'rebate_month':
            $whereClause = 'WHERE DATE_FORMAT(r.rebate_month, "%Y-%m") LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
        case 'notes':
            $whereClause = 'WHERE r.notes LIKE ?';
            $searchParams[] = "%$searchValue%";
            break;
    }
}


// 添加提现申请筛选条件
if (isset($_GET['withdrawal_requested']) && isset($_GET['is_withdrawn'])) {
    $withdrawalCondition = 'r.withdrawal_requested_date IS NOT NULL AND r.is_withdrawn = 0';
    $whereClause = $whereClause 
        ? $whereClause . ' AND ' . $withdrawalCondition 
        : 'WHERE ' . $withdrawalCondition;
}

// 获取总记录数
$totalQuery = "SELECT COUNT(*) as total FROM x_rebates r $whereClause";
$totalStmt = $pdo->prepare($totalQuery);
$totalStmt->execute($searchParams); // 只传递搜索参数
$totalRecords = $totalStmt->fetchColumn(); // 获取总记录数
$totalPages = ceil($totalRecords / $perPage); // 计算总页数

// 添加分页范围计算
$pageRange = 5; // 显示多少个页码按钮
$startPage = max(1, $page - floor($pageRange / 2));
$endPage = min($totalPages, $startPage + $pageRange - 1);

// 调整startPage以确保显示足够的页码
if ($endPage - $startPage + 1 < $pageRange && $startPage > 1) {
    $startPage = max(1, $endPage - $pageRange + 1);
}

// 获取当前页的提成记录信息
$sql = "SELECT r.*, s.name AS salesman_name, s.phone AS salesman_phone, a.netbar_account, a.netbar_name 
        FROM x_rebates r
        JOIN x_salesmen s ON r.salesman_id = s.salesman_id
        JOIN x_accounts a ON r.account_id = a.account_id
        $whereClause
        ORDER BY r.rebate_id DESC LIMIT :offset, :perPage";

$stmt = $pdo->prepare($sql);

// 修改这部分代码
if (!empty($searchParams)) {
    // 先处理 whereClause
    foreach ($searchParams as $index => $value) {
        $paramName = ':param' . $index;
        $whereClause = preg_replace('/\?/', $paramName, $whereClause, 1);
    }
    
    // 重新构建完整的 SQL 语句
    $sql = "SELECT r.*, s.name AS salesman_name, s.phone AS salesman_phone, a.netbar_account, a.netbar_name 
            FROM x_rebates r
            JOIN x_salesmen s ON r.salesman_id = s.salesman_id
            JOIN x_accounts a ON r.account_id = a.account_id
            $whereClause
            ORDER BY r.rebate_id DESC LIMIT :offset, :perPage";
            
    $stmt = $pdo->prepare($sql);
    
    // 绑定搜索参数
    foreach ($searchParams as $index => $value) {
        $paramName = ':param' . $index;
        $stmt->bindValue($paramName, $value);
    }
}

// 绑定分页参数
$stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
$stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);

$stmt->execute();

// 检查执行是否成功
if ($stmt->errorCode() !== '00000') {
    // 输出错误信息
    error_log('SQL Error: ' . print_r($stmt->errorInfo(), true));
}

// 获取当前页的提成记录
$commissions = $stmt->fetchAll(PDO::FETCH_ASSOC); // 获取当前页的提成记录
// 计算总金额
$totalAmount = 0;
$withdrawnAmount = 0;
$notWithdrawnAmount = 0;

foreach ($commissions as $commission) {
    $amount = floatval($commission['amount']);
    $totalAmount += $amount;
    
    if ($commission['is_withdrawn']) {
        $withdrawnAmount += $amount;
    } else {
        $notWithdrawnAmount += $amount;
    }
}
// ... existing code ...


// 获取业务员列表
$sql_salesmen = "SELECT * FROM x_salesmen";
$stmt_salesmen = $pdo->prepare($sql_salesmen);
$stmt_salesmen->execute();
$salesmen = $stmt_salesmen->fetchAll(PDO::FETCH_ASSOC);

// 获取网吧账户信息
$sql_accounts = "SELECT * FROM x_accounts";
$stmt_accounts = $pdo->prepare($sql_accounts);
$stmt_accounts->execute();
$accounts = $stmt_accounts->fetchAll(PDO::FETCH_ASSOC);

// 在获取提成记录的 SQL 查询之后，获取所有缴费记录的详细信息
$payment_ids = array_column($commissions, 'payment_id');
$payment_details = [];

if (!empty($payment_ids)) {
    // 获取缴费记录信息
    $payment_ids_str = implode(',', array_map('intval', $payment_ids));
    $sql_payments = "SELECT p.payment_id, p.version_id, p.package_ids, v.name as version_name 
                    FROM x_payments p
                    LEFT JOIN x_versions v ON p.version_id = v.version_id
                    WHERE p.payment_id IN ($payment_ids_str)";
    $stmt_payments = $pdo->query($sql_payments);
    $payments = $stmt_payments->fetchAll(PDO::FETCH_ASSOC);

    // 获取所有功能包信息
    $sql_packages = "SELECT package_id, name FROM x_extension_packages";
    $stmt_packages = $pdo->query($sql_packages);
    $packages = $stmt_packages->fetchAll(PDO::FETCH_KEY_PAIR);

    // 整理缴费记录的版本和功能包信息
    foreach ($payments as $payment) {
        $package_names = [];
        if (!empty($payment['package_ids'])) {
            $package_ids = json_decode($payment['package_ids'], true);
            foreach ($package_ids as $package_id) {
                if (isset($packages[$package_id])) {
                    $package_names[] = $packages[$package_id];
                }
            }
        }
        
        $payment_details[$payment['payment_id']] = [
            'version_name' => $payment['version_name'] ?? '',
            'package_names' => $package_names
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X管家业务系统 | 提成管理</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="assets/css/source-sans-pro.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <!-- Theme style -->
    <link rel="stylesheet" href="assets/css/adminlte.min.css">
    <style>
        .form-row {
            display: flex;
            flex-wrap: wrap;
        }
        .form-group {
            flex: 1 1 45%; /* 双列显示 */
            margin: 10px;
        }
        #avatarPreview {
            width: 100px;
            height: 100px;
            border: 1px solid #ccc;
            display: none; /* 初始隐藏 */
        }

        /* 状态样式 */
        .status-badge {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            white-space: nowrap;
            color: white; /* 文字颜色设为白色 */
            position: relative; /* 设置为相对定位 */
            display: inline-block; /* 让它适应内容 */
        }
        .status-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 0.25rem;
            z-index: -1; /* 使背景层在文字后面 */
        }

        .sortable { cursor: pointer; }
        .sortable:hover { background-color: #f8f9fa; }
        .sortable::after {
            content: '\f0dc';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            margin-left: 5px;
            color: #ddd;
        }
    </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
        <!-- Left navbar links -->
        <ul class="navbar-nav">
            <li class="nav-item">
                <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                    <i class="fas fa-bars"></i>
                </a>
            </li>
            <li class="nav-item d-none d-sm-inline-block">
                <a href="manage_commissions.php" class="nav-link active">提成管理</a>
            </li>
        </ul>

        <ul class="navbar-nav ml-auto">
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-bell"></i>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="#" role="button">
                    <i class="fas fa-user"></i>
                </a>
            </li>

            <!--begin::User Menu Dropdown-->
            <li class="nav-item dropdown user-menu" style="position: relative;">
              <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <img
                  src="<?php echo htmlspecialchars($user_avatar); ?>"
                  class="user-image rounded-circle shadow"
                  alt="User Image"
                />
                <span class="d-none d-md-inline"><?php echo htmlspecialchars($user_name); ?></span>
              </a>
              <ul class="dropdown-menu dropdown-menu-lg dropdown-menu-end" style="position: absolute; right: 0; left: auto; z-index: 1000;">
                <!--begin::User Image-->
                <li class="user-header" style="background-color: #007bff; color: white; padding: 10px; text-align: center;">
                  <img
                    src="<?php echo htmlspecialchars($user_avatar); ?>"
                    class="rounded-circle shadow"
                    alt="User Image"
                  />
                  <p>
                  <?php echo htmlspecialchars($user_name); ?>
                    <small><?php echo htmlspecialchars($user_email); ?></small>
                  </p>
                </li>
                <!--end::User Image-->
                <!--begin::Menu Body-->
                <li class="user-body">
                  <!--begin::Row-->
                  <div class="row text-center">
                    <div><a href="#">手机号码：<?php echo htmlspecialchars($user_phone); ?></a></div>
                  </div>
                  <!--end::Row-->
                </li>
                <!--end::Menu Body-->
                <!--begin::Menu Footer-->
                <li class="user-footer">
                  <a href="manage_users.php" class="btn btn-default btn-flat">个人信息</a>
                  <a href="logout.php" class="btn btn-default btn-flat float-end">退出系统</a>
                </li>
                <!--end::Menu Footer-->
              </ul>
            </li>
            <!--end::User Menu Dropdown-->
        </ul>
    </nav>

    <!-- Main Sidebar Container -->
    <?php include 'includes/sidebar.php'; ?>

    <!-- Content Wrapper -->
    <div class="content-wrapper">
        <!-- Content Header -->
        <section class="content-header">
            <div class="container-fluid">
                <div class="row mb-2">
                    <div class="col-sm-6">
                        <h1>提成管理</h1>
                    </div>
                    <div class="col-sm-6">
                        <ol class="breadcrumb float-sm-right">
                            <li class="breadcrumb-item"><a href="index.php">首页</a></li>
                            <li class="breadcrumb-item active">提成管理</li>
                        </ol>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main content -->
        <section class="content">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <div class="row">
                                    <div class="col-md-6">
                                        <form method="GET" action="manage_commissions.php" class="form-inline">
                                            <select class="form-control mr-2" name="search_field">
                                                <option value="salesman_name">业务员姓名</option>
                                                <option value="salesman_phone">业务员电话</option>
                                                <option value="netbar_account">网吧账户</option>
                                                <option value="netbar_name">网吧名称</option>
                                                <option value="payment_id">缴费记录ID</option>
                                                <option value="rebate_month">提成月份</option>
                                                <option value="notes">备注</option>
                                            </select>
                                            <input type="text" class="form-control mr-2" name="search_value" placeholder="请输入搜索关键词">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-search"></i> 搜索
                                            </button>
                                        </form>
                                    </div>

                                    <div class="col-md-6 text-right">
                                        <div class="btn-group gap-2">
                                            <button type="button" class="btn btn-warning me-2" onclick="showBatchPaymentModal()">
                                                <i class="fas fa-money-bill"></i> 一键设置已支付
                                            </button>
                                            <button type="button" class="btn btn-primary me-2" onclick="showWithdrawalRequests()">
                                                <i class="fas fa-list"></i> 显示申请提现列表
                                            </button>
                                            <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addCommissionModal">
                                                <i class="fas fa-plus"></i> 添加提成记录
                                            </button>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div class="card-body table-responsive p-0">
                                <table class="table table-hover text-nowrap" id="internetCafeTable">
                                    <thead>
                                        <tr>
                                            <th class="sortable"><input type="checkbox" id="selectAll" class="form-check-input"></th>
                                            <th class="sortable" data-sort="id">ID</th>
                                            <th class="sortable" data-sort="salesman_name">业务员</th>
                                            <th class="sortable" data-sort="netbar_account">网吧账户</th>
                                            <th class="sortable" data-sort="netbar_name">网吧名称</th>
                                            <th>缴费ID</th>
                                            <th>缴费项目</th>
                                            <th>提成金额</th>
                                            <th class="sortable" data-sort="rebate_month">提成月份</th>
                                            <th>创建时间</th>
                                            <th class="sortable" data-sort="withdrawal_allowed_date">允许提现</th>
                                            <th class="sortable" data-sort="withdrawal_requested_date">申请提现</th>
                                            <th class="sortable" data-sort="is_withdrawn">是否提现</th>
                                            <!-- <th class="sortable" data-sort="payment_method">支付方式</th> -->
                                            <th class="sortable" data-sort="payment_date">支付日期</th>
                                            <th class="sortable" data-sort="notes">备注</th>
                                            <!-- <th>更新时间</th> -->
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($commissions as $commission): ?>
                                            <tr>
                                                <td><input type="checkbox" class="form-check-input commission-checkbox" value="<?php echo htmlspecialchars($commission['rebate_id']); ?>"></td>
                                                <td><?php echo htmlspecialchars($commission['rebate_id']); ?></td>
                                                <td>
                                                    <span class="salesman-name" data-phone="<?php echo htmlspecialchars($commission['salesman_phone']); ?>">
                                                        <a href="?search_field=salesman_name&search_value=<?php echo urlencode($commission['salesman_name']); ?>">
                                                            <?php echo htmlspecialchars($commission['salesman_name']); ?>
                                                        </a>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="?search_field=netbar_account&search_value=<?php echo urlencode($commission['netbar_account']); ?>">
                                                        <?php echo htmlspecialchars($commission['netbar_account']); ?>
                                                    </a>
                                                </td>
                                                <td style="max-width: 160px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                                    <a href="?search_field=netbar_name&search_value=<?php echo urlencode($commission['netbar_name']); ?>">
                                                        <?php echo htmlspecialchars($commission['netbar_name']); ?>
                                                    </a>
                                                </td>
                                                <td><a href="view_payment.php?id=<?php echo htmlspecialchars($commission['payment_id']); ?>"><?php echo htmlspecialchars($commission['payment_id']); ?></a></td>
                                                <td>
                                                    <?php 
                                                    if (isset($payment_details[$commission['payment_id']])) {
                                                        $details = $payment_details[$commission['payment_id']];
                                                        echo htmlspecialchars($details['version_name']);
                                                        if (!empty($details['package_names'])) {
                                                            echo '<br><small class="text-muted">' . htmlspecialchars(implode(', ', $details['package_names'])) . '</small>';
                                                        }
                                                    } else {
                                                        echo '<span class="text-muted">-</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($commission['amount']); ?></td>
                                                <td>
                                                    <a href="?search_field=rebate_month&search_value=<?php echo urlencode(date('Y-m', strtotime($commission['rebate_month']))); ?>">
                                                        <?php echo htmlspecialchars(date('Y-m', strtotime($commission['rebate_month']))); ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars(date('Y-m-d', strtotime($commission['created_at']))); ?><br>
                                                    <?php echo htmlspecialchars(date('H:i:s', strtotime($commission['created_at']))); ?>
                                                </td>
                                                <td><?php echo htmlspecialchars(date('Y-m-d', strtotime($commission['withdrawal_allowed_date']))); ?></td>
                                                <td><?php echo htmlspecialchars($commission['withdrawal_requested_date']); ?></td>
                                                <td><?php echo htmlspecialchars($commission['is_withdrawn'] ? '是' : '否'); ?></td>
                                                <!-- <td><?php echo htmlspecialchars($commission['payment_method']); ?></td> -->
                                                <td>
                                                    <?php 
                                                    if (!empty($commission['payment_date'])) {
                                                        echo htmlspecialchars(date('Y-m-d', strtotime($commission['payment_date']))) . '<br>';
                                                        echo htmlspecialchars(date('H:i:s', strtotime($commission['payment_date'])));
                                                    } else {
                                                        echo '<span class="text-muted">-</span>';
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($commission['notes']); ?></td>
                                                <!-- <td><?php echo htmlspecialchars($commission['updated_at']); ?></td> -->
                                                <td>
                                                    <button class="btn btn-sm btn-warning" title="修改" 
                                                            onclick="window.location.href='edit_commission.php?id=<?php echo $commission['rebate_id']; ?>'">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-danger" title="删除" 
                                                            onclick="window.location.href='delete_commission.php?id=<?php echo $commission['rebate_id']; ?>'">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="card-footer clearfix">
                                <div class="float-left d-flex align-items-center">
                                    <form method="get" class="form-inline">
                                        <!-- 保持搜索参数 -->
                                        <?php if ($searchField && $searchValue): ?>
                                            <input type="hidden" name="search_field" value="<?php echo htmlspecialchars($searchField); ?>">
                                            <input type="hidden" name="search_value" value="<?php echo htmlspecialchars($searchValue); ?>">
                                        <?php endif; ?>
                                        <select class="form-control form-control-sm" name="per_page" onchange="this.form.submit()" style="width: 100px;">
                                            <?php foreach ($perPageOptions as $option): ?>
                                                <option value="<?php echo $option; ?>" <?php echo $perPage == $option ? 'selected' : ''; ?>>
                                                    <?php echo $option; ?>条/页
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </form>
                                </div>

                                <div class="float-right d-flex align-items-center">
                                    <span class="mr-2">共 <?php echo $totalRecords; ?> 条记录</span>
                                    <span class="mr-2">
                                        总金额：<span class="text-danger">￥<?php echo number_format($totalAmount, 2); ?></span>
                                        (<span class="text-success">已提现：￥<?php echo number_format($withdrawnAmount, 2); ?></span> | 
                                        <span class="text-primary">未提现：￥<?php echo number_format($notWithdrawnAmount, 2); ?></span>)
                                    </span>
                                    <ul class="pagination pagination-sm m-0 mr-2">

                                        <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?>&per_page=<?php echo $perPage; ?><?php 
                                                echo $searchField && $searchValue ? '&search_field='.urlencode($searchField).'&search_value='.urlencode($searchValue) : ''; 
                                            ?>">«</a>
                                        </li>
                                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>&per_page=<?php echo $perPage; ?><?php 
                                                    echo $searchField && $searchValue ? '&search_field='.urlencode($searchField).'&search_value='.urlencode($searchValue) : ''; 
                                                ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>
                                        <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?>&per_page=<?php echo $perPage; ?><?php 
                                                echo $searchField && $searchValue ? '&search_field='.urlencode($searchField).'&search_value='.urlencode($searchValue) : ''; 
                                            ?>">»</a>
                                        </li>
                                    </ul>
                                    <div class="input-group input-group-sm" style="width: 150px;">
                                        <input type="number" class="form-control" id="gotoPage" min="1" placeholder="页码" style="text-align: center;">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" type="button" onclick="gotoPage()">跳转</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="float-right d-none d-sm-block">
            <b>Version</b> 1.0.1
        </div>
        <strong>Copyright &copy; 2025 <a href="#">X管家业务系统</a>.</strong> All rights reserved.
    </footer>
</div>

<!-- jQuery -->
<script src="assets/js/jquery-3.7.1.min.js"></script>
<!-- Bootstrap 5 -->
<script src="assets/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE App -->
<script src="assets/js/adminlte.min.js"></script>

<!-- 添加提成记录模态框 -->
<div class="modal fade" id="addCommissionModal" tabindex="-1" aria-labelledby="addCommissionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header"> <!-- 移除 bg-primary 类 -->
                <h5 class="modal-title" id="addCommissionModalLabel">
                    <i class="fas fa-plus-circle"></i> 添加提成记录
                </h5>
                <button type="button" class="close" onclick="$('#addCommissionModal').modal('hide')" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body p-2"> <!-- 减小内边距 -->
                <form id="addCommissionForm">
                    <input type="hidden" name="action" value="add_commission">
                    
                    <!-- 基本信息卡片 -->
                    <div class="card card-primary card-outline mb-2"> <!-- 减小下边距 -->
                        <div class="card-header py-2"> <!-- 减小卡片头部的内边距 -->
                            <h3 class="card-title"><i class="fas fa-user"></i> 基本信息</h3>
                        </div>
                        <div class="card-body p-2"> <!-- 减小卡片内容的内边距 -->

                            <div class="form-group mt-3">
                                <label class="form-label">网吧账户</label>
                                <small class="text-muted" id="accountInfoDisplay">
                                    识别结果: <span id="netbarNameDisplay" class="text-primary"></span>
                                    (账户ID: <span id="accountIdDisplay" class="text-primary"></span>)
                                </small>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="netbar_account" required id="netbarAccountInput" placeholder="输入网吧账户">
                                    <button type="button" class="btn btn-outline-primary" id="searchAccountBtn">
                                        <i class="fas fa-search"></i> 查询账户
                                    </button>
                                </div>
                                <input type="hidden" name="account_id" id="accountIdField" required>

                            </div>

                            <div class="row g-2"> <!-- 使用更小的间距 -->
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">业务员</label>
                                        <div class="input-group">
                                            <select class="form-select" name="salesman_id" required>
                                                <option value="">请选择业务员</option>
                                                <?php foreach ($salesmen as $salesman): ?>
                                                    <option value="<?php echo $salesman['salesman_id']; ?>">
                                                        <?php echo htmlspecialchars($salesman['name']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">缴费ID</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
                                            <input type="text" class="form-control" name="payment_id" placeholder="输入缴费ID" required>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">提成金额</label>
                                        <div class="input-group">
                                            <span class="input-group-text">¥</span>
                                            <input type="number" class="form-control" name="amount" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- 时间信息卡片 -->
                    <div class="card card-info card-outline mb-2"> <!-- 减小下边距 -->
                        <div class="card-header py-2"> <!-- 减小卡片头部的内边距 -->
                            <h3 class="card-title"><i class="fas fa-calendar"></i> 时间信息</h3>
                        </div>
                        <div class="card-body p-2"> <!-- 减小卡片内容的内边距 -->
                            <div class="row g-2"> <!-- 使用更小的间距 -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">提成月份</label>
                                        <input type="month" class="form-control" name="rebate_month" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">允许提现时间</label>
                                        <input type="datetime-local" class="form-control" name="withdrawal_allowed_date">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提现信息卡片 -->
                    <div class="card card-success card-outline mb-2"> <!-- 减小下边距 -->
                        <div class="card-header py-2"> <!-- 减小卡片头部的内边距 -->
                            <h3 class="card-title"><i class="fas fa-money-bill"></i> 提现信息</h3>
                        </div>
                        <div class="card-body p-2"> <!-- 减小卡片内容的内边距 -->
                            <div class="row g-2"> <!-- 使用更小的间距 -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">是否已提现</label>
                                        <div class="form-group">
                                            <select class="form-select" name="is_withdrawn" required>
                                                <option value="0">未提现</option>
                                                <option value="1">已提现</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">申请提现时间</label>
                                        <input type="datetime-local" class="form-control" name="withdrawal_requested_date">
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">支付方式</label>
                                        <input type="text" class="form-control" name="payment_method">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label">支付日期</label>
                                        <input type="datetime-local" class="form-control" name="payment_date">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group mt-3">
                                <label class="form-label">备注</label>
                                <textarea class="form-control" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer py-1"> <!-- 减小页脚的内边距 -->
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" class="btn btn-sm btn-primary" onclick="submitAddCommissionForm()">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>


<!-- 批量支付确认模态框 -->
<div class="modal fade" id="batchPaymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title">
                    <i class="fas fa-money-bill-wave mr-2"></i>确认批量支付
                </h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <!-- 支付信息卡片 -->
                <div class="card card-outline card-info mb-3">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-info-circle mr-2"></i>支付明细
                        </h3>
                    </div>
                    <div class="card-body p-0">
                        <div id="paymentSummary" class="p-3"></div>
                    </div>
                </div>
                
                <!-- 支付方式选择 -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-credit-card mr-2"></i>支付方式
                    </label>
                    <select class="form-control" id="batchPaymentMethod">
                        <option value="微信">
                            <i class="fab fa-weixin"></i> 微信
                        </option>
                        <option value="支付宝">
                            <i class="fab fa-alipay"></i> 支付宝
                        </option>
                        <option value="银行转账">
                            <i class="fas fa-university"></i> 银行转账
                        </option>
                    </select>
                </div>
            </div>
            <div class="modal-footer justify-content-between">
                <button type="button" class="btn btn-default" data-bs-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-info" onclick="confirmBatchPayment()">
                    <i class="fas fa-check mr-1"></i>确认支付
                </button>
            </div>
        </div>
    </div>
</div>



<script>

function showBatchPaymentModal() {
    const selectedIds = getSelectedIds();
    if (selectedIds.length === 0) {
        alert('请先选择要处理的记录');
        return;
    }

    // 收集选中记录的信息
    let salesmanSummary = {};
    let totalAmount = 0;
    let totalCount = 0;
    
    selectedIds.forEach(id => {
        const row = document.querySelector(`input[value="${id}"]`).closest('tr');
        const salesmanName = row.querySelector('.salesman-name a').textContent.trim();
        const amount = parseFloat(row.querySelector('td:nth-child(8)').textContent);
        
        if (!salesmanSummary[salesmanName]) {
            salesmanSummary[salesmanName] = {
                amount: 0,
                count: 0
            };
        }
        salesmanSummary[salesmanName].amount += amount;
        salesmanSummary[salesmanName].count += 1;
        totalAmount += amount;
        totalCount += 1;
    });

    // 生成汇总信息HTML
    let summaryHtml = `
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> 支付汇总信息</h6>
            <p>共选择 ${totalCount} 条记录，涉及 ${Object.keys(salesmanSummary).length} 位业务员</p>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered table-sm">
                <thead>
                    <tr>
                        <th>业务员</th>
                        <th>记录数</th>
                        <th>金额</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    for (let [name, data] of Object.entries(salesmanSummary)) {
        summaryHtml += `
            <tr>
                <td>${name}</td>
                <td>${data.count} 条</td>
                <td>￥${data.amount.toFixed(2)}</td>
            </tr>
        `;
    }
    
    summaryHtml += `
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th>总计</th>
                        <th>${totalCount} 条</th>
                        <th>￥${totalAmount.toFixed(2)}</th>
                    </tr>
                </tfoot>
            </table>
        </div>
    `;
    
    document.getElementById('paymentSummary').innerHTML = summaryHtml;
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('batchPaymentModal'));
    modal.show();
}



function confirmBatchPayment() {
    const selectedIds = getSelectedIds();
    const paymentMethod = document.getElementById('batchPaymentMethod').value;
    const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
    
    fetch('manage_commissions.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'batch_payment',
            ids: selectedIds,
            payment_method: paymentMethod,
            payment_date: currentTime
        })
    })
    .then(response => response.text())  // 改为先获取文本响应
    .then(text => {
        let data;
        try {
            data = JSON.parse(text);  // 尝试解析 JSON
            if (data.success) {
                alert('批量支付设置成功！');
                const modal = bootstrap.Modal.getInstance(document.getElementById('batchPaymentModal'));
                modal.hide();
                window.location.reload();
            } else {
                throw new Error(data.message || '操作失败');
            }
        } catch (e) {
            console.error('Response:', text);  // 输出原始响应内容
            if (text.includes('success') && !text.includes('error')) {
                // 如果响应中包含 success 但不包含 error，认为是成功的
                alert('批量支付设置成功！');
                const modal = bootstrap.Modal.getInstance(document.getElementById('batchPaymentModal'));
                modal.hide();
                window.location.reload();
            } else {
                throw new Error('服务器响应格式错误，请联系管理员');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert(error.message || '操作失败，请稍后重试');
    });
}



// 全选/取消全选功能
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.getElementsByClassName('commission-checkbox');
    for (let checkbox of checkboxes) {
        checkbox.checked = this.checked;
    }
});

// 获取选中的ID列表
function getSelectedIds() {
    const checkboxes = document.getElementsByClassName('commission-checkbox');
    const selectedIds = [];
    for (let checkbox of checkboxes) {
        if (checkbox.checked) {
            selectedIds.push(checkbox.value);
        }
    }
    return selectedIds;
}


function showWithdrawalRequests() {
    // 获取当前的搜索参数
    const currentParams = new URLSearchParams(window.location.search);
    
    // 保留现有的搜索条件
    const newParams = new URLSearchParams();
    
    // 复制所有现有参数
    for (const [key, value] of currentParams.entries()) {
        if (key !== 'withdrawal_requested' && key !== 'is_withdrawn') {
            newParams.append(key, value);
        }
    }
    
    // 添加提现申请的筛选条件
    newParams.append('withdrawal_requested', 'notnull');
    newParams.append('is_withdrawn', '0');
    
    // 跳转到带有新参数的页面
    window.location.href = `manage_commissions.php?${newParams.toString()}`;
}



// 查询网吧账户按钮点击事件
document.getElementById('searchAccountBtn').addEventListener('click', function() {
    const netbarAccount = document.getElementById('netbarAccountInput').value;
    if (!netbarAccount) {
        alert('请输入网吧账户');
        return;
    }

    fetch(`../api/account.php?netbar_account=${encodeURIComponent(netbarAccount)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                document.getElementById('accountIdField').value = data.data.account_id;
                document.getElementById('netbarNameDisplay').textContent = data.data.netbar_name;
                document.getElementById('accountIdDisplay').textContent = data.data.account_id;
                // 自动填充关联的网吧名称字段
                document.getElementById('netbarAccountInput').value = data.data.netbar_account;
            } else {
                document.getElementById('accountIdField').value = '';
                document.getElementById('netbarNameDisplay').textContent = '未找到匹配账户';
                document.getElementById('accountIdDisplay').textContent = '';
                alert('未找到匹配的网吧账户,请检查输入');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('查询失败,请稍后重试');
        });
});

// 表单提交前验证
document.getElementById('addCommissionForm').addEventListener('submit', function(e) {
    if (!document.getElementById('accountIdField').value) {
        e.preventDefault();
        alert('请先查询并确认网吧账户信息');
    }
});

function submitAddCommissionForm() {
    // 获取保存按钮并禁用它
    const saveButton = document.querySelector('#addCommissionModal .modal-footer .btn-primary');
    saveButton.disabled = true;
    saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';

    const form = document.getElementById('addCommissionForm');
    const formData = new FormData(form);

    fetch('manage_commissions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('提成记录添加成功！');
            const modal = document.getElementById('addCommissionModal');
            const bsModal = bootstrap.Modal.getInstance(modal);
            bsModal.hide();
            window.location.reload();
        } else {
            console.log('添加提成记录失败：', data); // 调试信息
            alert('添加提成记录失败：' + data.message);
            // 恢复按钮状态
            saveButton.disabled = false;
            saveButton.innerHTML = '<i class="fas fa-save"></i> 保存';
        }
    })
    .catch(error => { 
        console.error('Error:', error); 
        alert('添加提成记录失败：' + error.message);
        // 恢复按钮状态
        saveButton.disabled = false;
        saveButton.innerHTML = '<i class="fas fa-save"></i> 保存';
    });
}

// 表格排序功能
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('internetCafeTable');
    const headers = table.querySelectorAll('th.sortable');
    let currentSort = {
        column: null,
        direction: 'asc'
    };

    headers.forEach(header => {
        header.addEventListener('click', () => {
            const column = header.dataset.sort;
            const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
            
            headers.forEach(h => h.classList.remove('asc', 'desc'));
            header.classList.add(direction);
            
            currentSort = { column, direction };
            
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            const sortedRows = rows.sort((a, b) => {
                const aValue = getCellValue(a, column);
                const bValue = getCellValue(b, column);
                return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            });
            
            tbody.innerHTML = '';
            sortedRows.forEach(row => tbody.appendChild(row));
        });
    });

    function getCellValue(row, column) {
        const cell = row.querySelector(`td:nth-child(${getColumnIndex(column)})`);
        return cell ? cell.textContent.trim() : '';
    }

    function getColumnIndex(column) {
        const headers = Array.from(table.querySelectorAll('th'));
        return headers.findIndex(header => header.dataset.sort === column) + 1;
    }
});
// 修改 gotoPage 函数
function gotoPage() {
    const page = document.getElementById('gotoPage').value;
    const perPage = <?php echo $perPage; ?>;
    if (page > 0 && page <= <?php echo $totalPages; ?>) {
        let url = `?page=${page}&per_page=${perPage}`;
        <?php if ($searchField && $searchValue): ?>
            url += '&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>';
        <?php endif; ?>
        window.location.href = url;
    } else {
        alert('请输入有效的页码');
    }
}
</script>

<!-- 添加业务员电话的提示标签 -->
<div id="salesmanPhoneTooltip" style="display:none; position:absolute; background-color:#fff; border:1px solid #ccc; padding:5px; z-index:1000;"></div>

<script>
// 查询网吧账户按钮点击事件
document.getElementById('searchAccountBtn').addEventListener('click', function() {
    const netbarAccount = document.getElementById('netbarAccountInput').value;
    if (!netbarAccount) {
        alert('请输入网吧账户');
        return;
    }

    fetch(`../api/account.php?netbar_account=${encodeURIComponent(netbarAccount)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                document.getElementById('accountIdField').value = data.data.account_id;
                document.getElementById('netbarNameDisplay').textContent = data.data.netbar_name;
                document.getElementById('accountIdDisplay').textContent = data.data.account_id;
                // 自动填充关联的网吧名称字段
                document.getElementById('netbarAccountInput').value = data.data.netbar_account;
            } else {
                document.getElementById('accountIdField').value = '';
                document.getElementById('netbarNameDisplay').textContent = '未找到匹配账户';
                document.getElementById('accountIdDisplay').textContent = '';
                alert('未找到匹配的网吧账户,请检查输入');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('查询失败,请稍后重试');
        });
});

// 表单提交前验证
document.getElementById('addCommissionForm').addEventListener('submit', function(e) {
    if (!document.getElementById('accountIdField').value) {
        e.preventDefault();
        alert('请先查询并确认网吧账户信息');
    }
});

function submitAddCommissionForm() {
    const form = document.getElementById('addCommissionForm');
    const formData = new FormData(form);

    fetch('manage_commissions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('提成记录添加成功！');
            const modal = document.getElementById('addCommissionModal');
            const bsModal = bootstrap.Modal.getInstance(modal);
            bsModal.hide();
            window.location.reload();
        } else {
            console.log('添加提成记录失败：', data); // 调试信息
            alert('添加提成记录失败：' + data.message);
        }
    })
        .catch(error => { 
            console.error('Error:', error); 
            alert('添加提成记录失败：' + error.message);
        });
}

// 表格排序功能
document.addEventListener('DOMContentLoaded', function() {
    const table = document.getElementById('internetCafeTable');
    const headers = table.querySelectorAll('th.sortable');
    let currentSort = {
        column: null,
        direction: 'asc'
    };

    headers.forEach(header => {
        header.addEventListener('click', () => {
            const column = header.dataset.sort;
            const direction = currentSort.column === column && currentSort.direction === 'asc' ? 'desc' : 'asc';
            
            headers.forEach(h => h.classList.remove('asc', 'desc'));
            header.classList.add(direction);
            
            currentSort = { column, direction };
            
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            const sortedRows = rows.sort((a, b) => {
                const aValue = getCellValue(a, column);
                const bValue = getCellValue(b, column);
                return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            });
            
            tbody.innerHTML = '';
            sortedRows.forEach(row => tbody.appendChild(row));
        });
    });

    function getCellValue(row, column) {
        const cell = row.querySelector(`td:nth-child(${getColumnIndex(column)})`);
        return cell ? cell.textContent.trim() : '';
    }

    function getColumnIndex(column) {
        const headers = Array.from(table.querySelectorAll('th'));
        return headers.findIndex(header => header.dataset.sort === column) + 1;
    }
});
// 修改 gotoPage 函数
function gotoPage() {
    const page = document.getElementById('gotoPage').value;
    const perPage = <?php echo $perPage; ?>;
    if (page > 0 && page <= <?php echo $totalPages; ?>) {
        let url = `?page=${page}&per_page=${perPage}`;
        <?php if ($searchField && $searchValue): ?>
            url += '&search_field=<?php echo urlencode($searchField); ?>&search_value=<?php echo urlencode($searchValue); ?>';
        <?php endif; ?>
        window.location.href = url;
    } else {
        alert('请输入有效的页码');
    }
}

// 修改这里：将业务员电话提示的代码移到正确的位置
$(document).ready(function() {
    $('.salesman-name').hover(function(event) {
        // 获取业务员电话
        const phone = $(this).data('phone');
        // 显示提示标签
        $('#salesmanPhoneTooltip').text('电话: ' + phone)
            .css({ top: event.pageY + 5, left: event.pageX + 5 }) // 设置位置
            .show();
    }, function() {
        // 隐藏提示标签
        $('#salesmanPhoneTooltip').hide();
    });
});
</script>
</body>
</html>